import React, { useState, useRef, useEffect } from 'react';
import { YTHLocalization, YTHList, YTHForm, YTHDialog } from 'yth-ui';
import { Button, Modal, Space, message } from 'antd';
import locales from '@/locales';
import dayjs from 'dayjs';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import analysisApi from '@/service/analysisApi';
import style from './index.module.less';

// 简化的类型定义
interface ReportRow {
  id: string;
  reportPath?: string;
}

interface FilterParams {
  reportMonth?: string;
  [key: string]: unknown;
}

interface PaginationParams {
  current: number;
  pageSize: number;
}

interface SubmitFormData {
  reportTime: string;
  reportName: string;
  [key: string]: string | number;
}

/**
 * @description 分析报告列表
 * @returns
 */
const ReportList: React.FC = () => {
  const listActionRef = useRef<ActionType>();
  const listAction = YTHList.createAction();
  const form = React.useMemo(() => YTHForm.createForm({}), []);
  const [visiable, setVisiable] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  /** 列表列配置 */
  const columns: IYTHColumnProps[] = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      display: false,
    },
    {
      dataIndex: 'reportName',
      title: '报告名称',
      width: 180,
      query: true,
      display: true,
    },
    {
      dataIndex: 'reportMonth',
      title: '报告月份',
      width: 180,
      query: true,
      display: true,
      componentName: 'DatePicker',
      componentProps: {
        precision: 'month',
        formatter: 'YYYY-MM',
        p_props: {
          placeholder: '请选择',
        },
      },
      render: (value: string) => {
        return value ? dayjs(value).format('YYYY-MM') : '-';
      },
    },
    {
      dataIndex: 'userIdText',
      title: '操作人',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'createDate',
      title: '生成时间',
      width: 180,
      query: false,
      display: true,
    },
  ];

  /** 提交保存 */
  const submitAddData = async (data: SubmitFormData) => {
    setLoading(true);
    const res = await analysisApi.generateAnalysisReport(data);
    setLoading(false);
    if (res && res.code && res.code === 200) {
      setVisiable(false);
      listAction.reload({});
    }
  };

  /** form 保存 */
  const formSave = () => {
    form.validate().then(() => {
      const submitData = {
        ...form.values,
        startTm: dayjs(form.values.reportTime).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
        endTm: dayjs(form.values.reportTime).endOf('month').format('YYYY-MM-DD HH:mm:ss'),
      };
      submitAddData(submitData);
    });
  };

  // 确认删除数据
  const confirmDelete = async (row: ReportRow) => {
    const res = await analysisApi.deleteReport(row.id);
    if (res && res.code && res.code === 200) {
      message.success('删除成功');
      listAction.reload({});
    }
  };

  // 处理查询过滤条件，将 reportMonth 转换为时间范围
  const processFilterCondition = (filter: FilterParams) => {
    const processedFilter = { ...filter };
    if (filter.reportMonth) {
      processedFilter.startTm = dayjs(filter.reportMonth)
        .startOf('month')
        .format('YYYY-MM-DD HH:mm:ss');
      processedFilter.endTm = dayjs(filter.reportMonth)
        .endOf('month')
        .format('YYYY-MM-DD HH:mm:ss');
      delete processedFilter.reportMonth; // 删除原始的 reportMonth 字段
    }
    return processedFilter;
  };

  // 下载报告文件
  const downloadReport = (row: ReportRow) => {
    if (row.reportPath) {
      const newPath = JSON.parse(row.reportPath);
      const url = newPath?.[0]?.url;
      const fileName = newPath?.[0]?.fileName;
      const downloadUrl = `${window.location.origin}${url}`;
      // 创建一个临时的 a 标签来触发下载
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = fileName || '化工园区空气站月报'; // 使用报告名称作为下载文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      message.warning('报告文件路径不存在');
    }
  };

  useEffect(() => {
    if (visiable) {
      form.setValues({
        reportTime: dayjs().format('YYYY-MM'),
        reportName: '',
      });
    }
  }, [visiable]);

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <YTHList
        defaultQuery={{}}
        code="analysisReportList"
        action={listAction}
        actionRef={listActionRef}
        showRowSelection={false}
        operation={[
          {
            element: (
              <div>
                <Button
                  size="small"
                  type="primary"
                  onClick={() => {
                    setVisiable(true);
                  }}
                >
                  生成分析报告
                </Button>
              </div>
            ),
          },
        ]}
        extraOperation={[]}
        listKey="id"
        request={async (filter: FilterParams, pagination: PaginationParams) => {
          const resData = await analysisApi.queryAnalysisReportList({
            descs: [''],
            condition: processFilterCondition(filter),
            currentPage: pagination.current,
            pageSize: pagination.pageSize,
          });
          if (resData.code && resData.code === 200) {
            const dataWithSerialNo = resData.data.map((item: any, index: number) => ({
              ...item,
              serialNo: (pagination.current - 1) * pagination.pageSize + index + 1,
            }));
            return {
              data: dataWithSerialNo,
              total: resData.total || 0,
              success: true,
            };
          }
          return {
            data: [],
            total: 0,
            success: false,
          };
        }}
        rowOperationWidth={100}
        rowOperation={(row: any) => {
          return [
            {
              element: (
                <div>
                  <Space size="small">
                    <Button
                      size="small"
                      type="link"
                      onClick={() => downloadReport(row)}
                      style={{ fontSize: 12 }}
                    >
                      下载
                    </Button>

                    <Button
                      size="small"
                      type="link"
                      danger
                      onClick={() => {
                        YTHDialog.show({
                          type: 'confirm',
                          content: <p>确认删除此条数据？</p>,
                          onCancle: () => {},
                          onConfirm: () => {
                            confirmDelete(row);
                          },
                          p_props: {
                            cancelText: '取消',
                            okText: '确定',
                            title: '删除',
                          },
                        });
                      }}
                      style={{ fontSize: 12 }}
                    >
                      删除
                    </Button>
                  </Space>
                </div>
              ),
            },
          ];
        }}
        columns={columns}
      />

      <Modal
        width="50%"
        title="生成分析报告"
        footer={null}
        visible={visiable}
        onCancel={() => setVisiable(false)}
        destroyOnClose
        maskClosable={false}
      >
        <YTHForm form={form} col={2}>
          <YTHForm.Item
            name="id"
            title="id"
            labelType={1}
            required={false}
            display="hidden"
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="reportTime"
            title="月报时间"
            labelType={1}
            required
            componentName="DatePicker"
            componentProps={{
              precision: 'month',
              formatter: 'YYYY-MM',
            }}
          />
          <YTHForm.Item
            name="reportName"
            title="报告名称"
            labelType={1}
            required
            componentName="Input"
            componentProps={{}}
          />
        </YTHForm>
        <div className={style['modal-footer']}>
          <Button onClick={() => setVisiable(false)}>取消</Button>
          <Button
            type="primary"
            loading={loading}
            onClick={() => {
              formSave();
            }}
            style={{ marginLeft: 20 }}
          >
            生成
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default YTHLocalization.withLocal(ReportList, locales, YTHLocalization.getLanguage());
