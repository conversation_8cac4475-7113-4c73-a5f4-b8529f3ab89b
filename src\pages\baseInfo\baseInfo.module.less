/* BaseInfo 页面样式 */

/* ===== 变量定义 ===== */
@font-size-small: 12px;
@font-size-medium: 14px;
@font-size-large: 18px;
@primary-color: #007ebb;
@warning-color: #faad14;
@danger-color: #f03;
@border-color: #d9d9d9;
@border-radius-small: 2px;
@border-radius-medium: 6px;
@background-white: #fff;
@spacing-small: 8px;
@spacing-medium: 10px;
@spacing-large: 20px;

/* ===== 全局组件样式统一 ===== */
:global {
  .ant-input,
  .ant-form-item-label > label,
  .ant-select-selector,
  .ant-picker,
  .ant-select-item-option-content,
  .ant-btn {
    font-size: @font-size-small;
  }
}

/* ===== 表单相关样式 ===== */
.antGasleakForm {
  margin-bottom: @spacing-small;
  background-color: @background-white;
  padding: @spacing-medium;
  height: 120px;
  border-radius: @border-radius-medium;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  /* 响应式设计 */
  @media (max-width: 768px) {
    height: auto;
    min-height: 100px;
    padding: @spacing-small;
  }
}

.formPicker {
  .ant-form-item-control-input {
    border: 1px solid @border-color;
    border-radius: @border-radius-small;
    transition: border-color 0.3s ease;

    &:hover {
      border-color: @primary-color;
    }

    &:focus-within {
      border-color: @primary-color;
      box-shadow: 0 0 0 2px fade(@primary-color, 20%);
    }
  }
}

/* ===== 模态框内容样式 ===== */
.modalContentCircle {
  display: flex;
  align-items: center;
  padding: @spacing-medium 0;

  .anticon {
    font-size: @font-size-large;
    padding-right: @spacing-medium;
    color: @warning-color;
    flex-shrink: 0;
  }

  /* 文本内容样式 */
  .contentText {
    font-size: @font-size-medium;
    line-height: 1.5;
    color: #333;
  }
}

/* ===== 状态指示样式 ===== */
.triangle {
  color: @danger-color;
  font-weight: bold;

  &::before {
    content: "▲";
    margin-right: 4px;
  }
}

/* ===== 按钮样式 ===== */
.topButton {
  margin: 0 @spacing-small;
  padding: 0 @spacing-medium;
  font-size: @font-size-small;
  border-radius: @border-radius-medium;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
  }
}

/* ===== 抽屉操作区域样式 ===== */
.drawerFilterOperation {
  width: 100%;
  margin-top: @spacing-medium;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: @spacing-medium;
  padding: @spacing-medium 0;
  border-top: 1px solid #f0f0f0;

  .searchBtn {
    background-color: @primary-color;
    border-color: @primary-color;
    color: white;
    font-size: @font-size-small;
    border-radius: @border-radius-medium;
    transition: all 0.3s ease;

    &:hover {
      background-color: darken(@primary-color, 10%);
      border-color: darken(@primary-color, 10%);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }
  }

  .resetBtn {
    font-size: @font-size-small;
    border-radius: @border-radius-medium;
    transition: all 0.3s ease;

    &:hover {
      border-color: @primary-color;
      color: @primary-color;
    }
  }

  .infoTip {
    margin-right: auto;
    font-size: @font-size-small;
    color: #666;
    display: flex;
    align-items: center;

    .anticon {
      margin-right: 4px;
      color: @warning-color;
    }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    flex-direction: column;
    gap: @spacing-small;

    .infoTip {
      margin-right: 0;
      margin-bottom: @spacing-small;
    }

    .searchBtn,
    .resetBtn {
      width: 100%;
    }
  }
}
