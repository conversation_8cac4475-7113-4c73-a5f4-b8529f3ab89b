/* TraceObjectSelector 组件样式 */

/* ===== 变量定义 ===== */
@primary-color: #1890ff;
@primary-light: #e6f7ff;
@primary-lighter: #bae7ff;
@hover-color: #f5f5f5;
@text-color-secondary: #999;
@border-color: #d9d9d9;
@border-radius: 6px;
@transition-duration: 0.2s;
@font-size-small: 12px;
@font-size-normal: 14px;

/* ===== 主容器样式 ===== */
.traceObjectSelector {
  padding: 20px;
  font-size: @font-size-small;

  /* ===== Transfer 组件样式 ===== */
  .transferContainer {
    width: 100%;

    /* Transfer 内部样式优化 */
    :global(.ant-transfer) {
      .ant-transfer-list {
        border: 1px solid @border-color;
        border-radius: @border-radius;

        .ant-transfer-list-header {
          font-size: @font-size-small;
          padding: 8px 12px;
        }

        .ant-transfer-list-body {
          font-size: @font-size-small;
        }

        .ant-transfer-list-search {
          padding: 8px;

          .ant-input {
            font-size: @font-size-small;
          }
        }
      }

      .ant-transfer-operation {
        margin: 0 16px;

        .ant-btn {
          font-size: @font-size-small;
        }
      }
    }
  }

  /* ===== 表格容器样式 ===== */
  .tableContainer {
    height: 400px;
    overflow: hidden;
    border-radius: @border-radius;

    /* 响应式设计 */
    @media (max-width: 768px) {
      height: 300px;
    }
  }

  /* ===== 表格样式 ===== */
  .table {
    height: 100%;

    /* 表格头部样式 */
    :global(.ant-table-thead > tr > th) {
      font-size: @font-size-small;
      font-weight: 500;
      background-color: #fafafa;
      border-bottom: 1px solid @border-color;
    }

    /* 表格主体样式 */
    :global(.ant-table-tbody) {
      > tr {
        transition: background-color @transition-duration ease;
        font-size: @font-size-small;

        > td {
          padding: 8px 12px;
          border-bottom: 1px solid #f0f0f0;
        }

        /* 悬停效果 */
        &:hover {
          background-color: @hover-color;
        }

        /* 选中行样式 */
        &.selected-row {
          background-color: @primary-light !important;

          &:hover {
            background-color: @primary-lighter !important;
          }
        }
      }
    }

    /* 空数据状态 */
    :global(.ant-empty) {
      .ant-empty-description {
        font-size: @font-size-small;
        color: @text-color-secondary;
      }
    }
  }

  /* ===== 可点击行样式 ===== */
  .clickableRow {
    cursor: pointer;
    user-select: none;

    &:active {
      transform: translateY(1px);
    }
  }
}

/* ===== 输入框样式 ===== */
.inputBox {
  color: @text-color-secondary !important;
  font-size: @font-size-small;

  &:hover {
    border-color: @primary-color;
  }

  &:focus {
    border-color: @primary-color;
    box-shadow: 0 0 0 2px fade(@primary-color, 20%);
  }
}

/* ===== 全局样式覆盖（谨慎使用） ===== */
:global {
  /* 模态框样式优化 */
  .ant-modal {
    .ant-modal-header {
      .ant-modal-title {
        font-size: @font-size-normal;
      }
    }

    .ant-modal-footer {
      .ant-btn {
        font-size: @font-size-small;
      }
    }
  }
}
